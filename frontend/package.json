{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "ts-error-check": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.4", "@reduxjs/toolkit": "^2.3.0", "@tanstack/react-table": "^8.21.3", "axios": "^1.7.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.460.0", "next": "15.0.3", "react": "19.0.0-rc-66855b96-20241106", "react-day-picker": "^9.7.0", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.54.2", "react-redux": "^9.1.2", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "video.js": "^8.19.1", "videojs-http-source-selector": "^1.1.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}