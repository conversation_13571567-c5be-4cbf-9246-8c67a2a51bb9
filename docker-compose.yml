services:

  db:
    image: postgres
    container_name: sm-db
    ports:
      - 5432:5432
    restart: always
    healthcheck:
      test: [ "CMD-SHELL", "sh -c 'pg_isready -U admin -d sm'" ]
      interval: 10s
      timeout: 3s
      retries: 3
    environment:
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: password
      POSTGRES_DB: sm
    volumes:
      - pgdata:/var/lib/postgresql/data
    networks:
      - nwtk_sales_manager

  adminer:
    image: adminer
    restart: always
    ports:
      - 4000:8080
    networks:
      - nwtk_sales_manager

  backend:
    build:
      context: ./backend
      target: development
    container_name: sm_backend
    environment:
      - NODE_ENV=development
      - DB_USER=admin
      - DB_PASSWORD=password
      - DB_NAME=sm
      - DB_HOST=sm-db
      - DB_PORT=5432
      - PORT=9052
      - REDIS_HOST=redis
    volumes:
      - ./backend:/app
      - ./backend/node_modules:/app/node_modules
      - ./backend/config:/app/config
    depends_on:
      db:
        condition: service_healthy
    ports:
      - '9052:9052'
    networks:
      - nwtk_sales_manager
    # command: yarn dev  # Use `yarn dev` for hot-reloading

  # frontend:
  #   build:
  #     context: ./frontend
  #   container_name: sm_frontend
  #   ports:
  #     - '3000:3000'
  #   volumes:
  #     - ./frontend:/app
  #     - /app/node_modules
  #   networks:
  #     - nwtk_sales_manager

  redis:
    image: 'bitnami/redis:latest'
    environment:
      - ALLOW_EMPTY_PASSWORD=yes
    networks:
      - nwtk_sales_manager

volumes:
  pgdata:
    driver: local

networks:
  nwtk_sales_manager:
    driver: bridge
