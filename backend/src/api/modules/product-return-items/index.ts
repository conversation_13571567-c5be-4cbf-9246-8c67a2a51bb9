import { Router } from 'express';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from '../../middlewares';
import { productReturnItemController } from '../bootstrap';
import { ProductReturnItemCreationValidationSchema, ProductReturnItemUpdateValidationSchema } from './validations';

export const ProductReturnItemRouter = Router();

ProductReturnItemRouter.route('/')
  .get([isAuthenticated], asyncCatchHandler(productReturnItemController.getReturnItems))
  .post(
    [isAuthenticated, validateRequestBody(ProductReturnItemCreationValidationSchema)],
    asyncCatchHand<PERSON>(productReturnItemController.createReturnItem)
  );

ProductReturnItemRouter.route('/:id')
  .get([isAuthenticated], asyncCatchHandler(productReturnItemController.getReturnItemById))
  .put(
    [isAuthenticated, validateRequestBody(ProductReturnItemUpdateValidationSchema)],
    async<PERSON><PERSON><PERSON>and<PERSON>(productReturnItemController.updateReturnItem)
  )
  .delete([isAuthenticated], asyncCatchHandler(productReturnItemController.deleteReturnItem));
