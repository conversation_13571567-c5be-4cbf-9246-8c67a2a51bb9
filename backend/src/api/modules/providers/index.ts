import { Router } from 'express';
import {
  asyncCatchHandler,
  isAuthenticated,
  validateRequestBody,
} from '../../middlewares';
import { providerController } from '../bootstrap';
import { ProviderBodyValidationSchema } from './validations';

export const ProviderRouter = Router();

ProviderRouter.route('/')
  .get([isAuthenticated], async<PERSON>atchHandler(providerController.findProviders))
  .post(
    [isAuthenticated, validateRequestBody(ProviderBodyValidationSchema)],
    asyncCatch<PERSON>and<PERSON>(providerController.createProvider),
  );

ProviderRouter.route('/:providerId')
  .patch(
    [isAuthenticated, validateRequestBody(ProviderBodyValidationSchema)],
    async<PERSON><PERSON><PERSON>and<PERSON>(providerController.updateProvider),
  )
  .delete(
    [isAuthenticated],
    async<PERSON>atch<PERSON>and<PERSON>(providerController.deleteProvider),
  );
