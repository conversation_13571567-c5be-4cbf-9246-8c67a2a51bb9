import { Router } from 'express';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from '../../middlewares';
import { paymentController } from '../bootstrap';
import { PaymentCreationValidationSchema, PaymentUpdateValidationSchema } from './validations';

export const PaymentRouter = Router();

PaymentRouter.route('/')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(paymentController.getPayments))
  .post(
    [isAuthenticated, validateRequestBody(PaymentCreationValidationSchema)],
    async<PERSON>atch<PERSON>and<PERSON>(paymentController.createPayment)
  );

PaymentRouter.route('/:paymentId')
  .get([isAuthenticated], asyncCatch<PERSON>andler(paymentController.getPaymentById))
  .patch(
    [isAuthenticated, validateRequestBody(PaymentUpdateValidationSchema)],
    async<PERSON><PERSON><PERSON><PERSON><PERSON>(paymentController.updatePayment)
  )
  .delete([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(paymentController.deletePayment));