import { Router } from 'express';
import { asyncCatchHand<PERSON>, isAuthenticated, validateRequestBody } from '../../middlewares';
import { userController } from '../bootstrap';
import { UserBodyValidationSchema } from './validations';

export const UserRouter = Router();

UserRouter.route('/')
  .post([validateRequestBody(UserBodyValidationSchema)], async<PERSON><PERSON><PERSON><PERSON><PERSON>(userController.createUser))

UserRouter.route('/:userId')
  .get([], async<PERSON>atch<PERSON>andler(userController.getUserWithAllInfo))