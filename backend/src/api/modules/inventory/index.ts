import { Router } from 'express';
import {
  asyncCatchHandler,
  isAuthenticated,
  validateRequestBody,
} from '../../middlewares';
import { inventoryController } from '../bootstrap';
import { InventoryCreationValidationSchema, InventoryUpdateValidationSchema } from './validations';

export const InventoryRouter = Router();

InventoryRouter.route('/')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(inventoryController.findInventory))
  .post(
    [isAuthenticated, validateRequestBody(InventoryCreationValidationSchema)],
    async<PERSON>atch<PERSON>and<PERSON>(inventoryController.createInventory),
  );

InventoryRouter.route('/stats')
  .get([isAuthenticated], async<PERSON>atchHandler(inventoryController.getInventoryStats));

InventoryRouter.route('/low-stock')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(inventoryController.getLowStockItems));

InventoryRouter.route('/:inventoryId')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(inventoryController.findInventoryById))
  .patch(
    [isAuthenticated, validateRequestBody(InventoryUpdateValidationSchema)],
    asyncCatchHandler(inventoryController.updateInventory),
  )
  .delete(
    [isAuthenticated],
    asyncCatchHandler(inventoryController.deleteInventory),
  );