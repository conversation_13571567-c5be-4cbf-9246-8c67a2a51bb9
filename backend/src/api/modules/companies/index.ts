import { Router } from 'express';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from '../../middlewares';
import { companyController } from '../bootstrap';
import { CompanyCreationValidationSchema } from './validations';

export const CompanyRouter = Router();

CompanyRouter.route('/')
  .get([isAuthenticated], asyncCatch<PERSON>andler(companyController.getCompanies))
  .post([isAuthenticated, validateRequestBody(CompanyCreationValidationSchema)], asyncCatch<PERSON>and<PERSON>(companyController.createCompany));

