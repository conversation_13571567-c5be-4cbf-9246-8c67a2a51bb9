import { Router } from 'express';
import {
  asyncCatchHandler,
  isAuthenticated,
  validateRequestBody,
} from '../../middlewares';
import { credentialController } from '../bootstrap';
import {
  CredentialBodyValidationSchema,
  LoginCredentialBodyValidationSchema,
  SwitchCompanyBodyValidationSchema,
} from './validations';

export const CredentialRouter = Router();

CredentialRouter.route('/').post(
  [validateRequestBody(CredentialBodyValidationSchema)],
  async<PERSON>atch<PERSON>andler(credentialController.createCredential),
);

CredentialRouter.route('/login').post(
  [validateRequestBody(LoginCredentialBodyValidationSchema)],
  async<PERSON><PERSON><PERSON><PERSON><PERSON>(credentialController.loginCredential),
);

CredentialRouter.route('/me')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(credentialController.getMe))
  .post([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(credentialController.refreshCredential))
  .delete([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(credentialController.logout));

CredentialRouter.route('/switch-company').post(
  [validateRequestBody(SwitchCompanyBodyValidationSchema), isAuthenticated],
  asyncCatchHandler(credentialController.switchCompany),
);
