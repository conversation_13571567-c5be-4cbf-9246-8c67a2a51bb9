import { Router } from 'express';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from '../../middlewares';
import { employeeController } from '../bootstrap';
import { EmployeeCreationValidationSchema } from './validations';

export const EmployeeRouter = Router();

EmployeeRouter.route('/')
  .post([], async<PERSON><PERSON><PERSON><PERSON><PERSON>(employeeController.createEmployee));

// EmployeeRouter.route('/channels')
//   .post([validateRequestBody(EmployeeCreationValidationSchema)], async<PERSON><PERSON><PERSON><PERSON><PERSON>(employeeController.createEmployeeAndAssignToChannel));

