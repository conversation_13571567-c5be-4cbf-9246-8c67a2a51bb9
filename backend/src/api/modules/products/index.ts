import { Router } from 'express';
import {
  asyncCatchHandler,
  isAuthenticated,
  validateRequestBody,
} from '../../middlewares';
import { productController } from '../bootstrap';
import { ProductBodyValidationSchema } from './validations';

export const ProductRouter = Router();

ProductRouter.route('/')
  .get([isAuthenticated], async<PERSON>atch<PERSON><PERSON>ler(productController.findProducts))
  .post(
    [isAuthenticated, validateRequestBody(ProductBodyValidationSchema)],
    async<PERSON>atch<PERSON>and<PERSON>(productController.createProduct),
  );

ProductRouter.route('/:productId')
  .patch(
    [isAuthenticated, validateRequestBody(ProductBodyValidationSchema)],
    async<PERSON><PERSON><PERSON><PERSON><PERSON>(productController.updateProduct),
  )
  .delete(
    [isAuthenticated],
    asyncCatchHandler(productController.deleteProduct),
  );
