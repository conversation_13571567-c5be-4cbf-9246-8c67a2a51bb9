import { Router } from 'express';
import { BillCreationValidationSchema, BillUpdateValidationSchema } from './validations';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from 'src/api/middlewares';
import { billController } from '../bootstrap';

export const BillRouter = Router();

BillRouter.post('/', [isAuthenticated, validateRequestBody(BillCreationValidationSchema)], async<PERSON><PERSON><PERSON><PERSON><PERSON>(billController.createBill));
BillRouter.get('/', [isAuthenticated], async<PERSON>atch<PERSON>and<PERSON>(billController.findBills));
BillRouter.get('/:id', [isAuthenticated], async<PERSON>atch<PERSON><PERSON><PERSON>(billController.findBillById))
    .patch('/:id', [isAuthenticated, validateRequestBody(BillUpdateValidationSchema)], async<PERSON><PERSON><PERSON><PERSON><PERSON>(billController.updateBill))
    .delete('/:id', [isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(billController.deleteBill));

