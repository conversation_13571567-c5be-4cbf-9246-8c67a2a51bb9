import { Router } from 'express';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from '../../middlewares';
import { zoneController } from '../bootstrap';
import { ZoneCreationValidationSchema, ZoneUpdateValidationSchema } from './validations';

export const ZoneRouter = Router();

ZoneRouter.route('/')
  .get([isAuthenticated], async<PERSON>atch<PERSON>and<PERSON>(zoneController.getZones))
  .post(
    [isAuthenticated, validateRequestBody(ZoneCreationValidationSchema)],
    asyncCatchHandler(zoneController.createZone)
  );

ZoneRouter.route('/:zoneId')
  .get([isAuthenticated], async<PERSON>atch<PERSON>and<PERSON>(zoneController.getZoneById))
  .patch(
    [isAuthenticated, validateRequestBody(ZoneUpdateValidationSchema)],
    async<PERSON><PERSON><PERSON><PERSON><PERSON>(zoneController.updateZone)
  )
  .delete([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(zoneController.deleteZone));