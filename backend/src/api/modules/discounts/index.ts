import { Router } from 'express';
import { asyncCatchHandler, isAuthenticated, validateRequestBody } from '../../middlewares';
import { discountController } from '../bootstrap';
import { DiscountCreationValidationSchema, DiscountUpdateValidationSchema } from './validations';

export const DiscountRouter = Router();

DiscountRouter.route('/')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(discountController.getDiscounts))
  .post(
    [isAuthenticated, validateRequestBody(DiscountCreationValidationSchema)],
    async<PERSON>atch<PERSON>and<PERSON>(discountController.createDiscount)
  );

DiscountRouter.route('/active')
  .get([isAuthenticated], async<PERSON>atch<PERSON>andler(discountController.getActiveDiscounts));

DiscountRouter.route('/:discountId')
  .get([isAuthenticated], async<PERSON>atch<PERSON><PERSON><PERSON>(discountController.getDiscountById))
  .patch(
    [isAuthenticated, validateRequestBody(DiscountUpdateValidationSchema)],
    async<PERSON><PERSON><PERSON><PERSON><PERSON>(discountController.updateDiscount)
  )
  .delete([isAuthenticated], asyncCatchHandler(discountController.deleteDiscount));