import { Router } from 'express';
import {
  asyncCatchHandler,
  isAuthenticated,
  validateRequestBody,
} from '../../middlewares';
import { roleController } from '../bootstrap';
import { RoleBodyValidationSchema } from './validations';

export const RoleRouter = Router();

RoleRouter.route('/')
  .get([isAuthenticated], async<PERSON><PERSON><PERSON><PERSON><PERSON>(roleController.findRoles))
  .post(
    [isAuthenticated, validateRequestBody(RoleBodyValidationSchema)],
    async<PERSON>atch<PERSON><PERSON><PERSON>(roleController.createRole),
  );

RoleRouter.route('/:roleId')
  .patch(
    [isAuthenticated, validateRequestBody(RoleBodyValidationSchema)],
    async<PERSON><PERSON><PERSON><PERSON><PERSON>(roleController.updateRole),
  )
  .delete([isAuthenticated], asyncCatchHandler(roleController.deleteRole));
