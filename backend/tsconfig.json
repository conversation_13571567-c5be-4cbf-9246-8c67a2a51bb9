{"compilerOptions": {"target": "es6", "module": "commonjs", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": "./src", "typeRoots": ["./node_modules/@types", "./src/types"], "paths": {"*": ["node_modules/*", "src/types/*"], "src/*": ["*"]}}, "include": ["src/**/*"]}