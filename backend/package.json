{"name": "sales_manager_backend", "version": "1.0.0", "main": "index.js", "license": "MIT", "scripts": {"dev": "ts-node-dev --respawn --transpile-only --quiet -r tsconfig-paths/register ./src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@sequelize/core": "7.0.0-alpha.37", "@sequelize/postgres": "7.0.0-alpha.44", "axios": "^1.7.7", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "mysql2": "^3.11.4", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "sequelize": "^6.37.5", "sequelize-typescript": "^2.1.6", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "winston": "^3.17.0", "zod": "^3.23.8"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.9.0", "@types/sequelize": "^4.28.20", "@types/supertest": "^6.0.3", "@types/validator": "^13.12.2", "jest": "^30.0.4", "sequelize-cli": "^6.6.2", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.6.3"}}